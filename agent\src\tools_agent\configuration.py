from pydantic import BaseModel, Field

class MCPConfig(BaseModel):
    name: str = Field(description = "Name of the MCP server. Used for the client only, does not need to match the actual server name.")
    url: str = Field(description = "URL of the MCP server.")
    transport: str = Field(default = "streamable_http", description = "Transport protocol for the MCP server. Default is 'streamable_http'.")

class AgentConfig(BaseModel):
    system_prompt: str = Field(
        default = "You are a helpful AI assistant.",
        description = "System prompt to guide the agent's behavior."
    )
    model: str = Field(
        default = "azure_openai:gpt-4.1",
        description = "Model to use for the agent. The correct format is 'provider:model'. Default is 'azure_openai:gpt-4.1'."
    )
    mcp_configs: list[MCPConfig] = Field(
        default_factory = list,
        description = "List of MCP server configurations for the agent."
    )
