apiVersion: v1
kind: Secret
metadata:
  name: weaviate-secrets
  labels:
    app: sales-agent
    component: search
    tier: backend
    environment: production
stringData:
  WEAVIATE_HTTP_HOST: weaviate
  WEAVIATE_HTTP_PORT: "80"
  WEAVIATE_GRPC_HOST: weaviate-grpc
  WEAVIATE_GRPC_PORT: "50051"
  # This key is used to connect to weaviate by MCP Search service
  WEAVIATE_API_KEY: api-key
  VECTOR_STORE_COLLECTION: "rag_chunks"

  # This key is for deploying weaviate with its Helm chart
  AUTHENTICATION_APIKEY_ALLOWED_KEYS: api-key
