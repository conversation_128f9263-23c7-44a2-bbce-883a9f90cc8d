from src.utils.doc_with_source import DocWithSource
from src.utils.md_mongo_loader import MDMongoLoader
from src.utils.chunking import ChunkingUtils
from src.utils.save_to_vector_store import DocsSaver

import os
from dotenv import load_dotenv

import weaviate
from weaviate.classes.init import Auth

from src.database.vector_store import VectorStoreManager

from src.models.embedding import EmbeddingModelForVectorStore

load_dotenv()


def main():
    try:
        embedding_model = EmbeddingModelForVectorStore()

        client = weaviate.connect_to_custom(
                http_host=os.getenv("WEAVIATE_HTTP_HOST"),
                http_port=os.getenv("WEAVIATE_HTTP_PORT"),
                http_secure=False,
                grpc_host=os.getenv("WEAVIATE_GRPC_HOST"),
                grpc_port=os.getenv("WEAVIATE_GRPC_PORT"),
                grpc_secure=False,
                auth_credentials=Auth.api_key(os.getenv("WEAVIATE_API_KEY"))
            )
        vector_store = None
        
        domains_list = os.getenv("DOMAINS_LIST", "").split(",")

        mongo_loader = MDMongoLoader(mongo_url=os.getenv("MONGO_URL"), database_name=os.getenv("MONGO_DATABASE_NAME"))


        for domain in domains_list:
            collection_name = mongo_loader.get_latest_collection_name(prefix=domain)
            print(collection_name)
            if not collection_name:
                print(f"No collections found for domain: {domain}")
                continue
            
            print(f"Processing domain: {domain}, collection: {collection_name}")

            docs = mongo_loader.loadAllMarkDown_fromMongoDB(collectionName=collection_name)

            if not docs:
                print(f"No documents found in collection: {collection_name}")
                continue

            print(f"Loaded {len(docs)} documents from MongoDB for domain: {domain}")

            chunking_utils = ChunkingUtils(chunk_size=2000, chunk_overlap=400)
            chunking_utils.addChunkListsTo(docs)

            print(f"Chunked documents into {sum(len(doc.chunkList) for doc in docs)} chunks for domain: {domain}")

            vector_store = VectorStoreManager(
                store_type="weaviate",
                embedding_model=embedding_model.embedding_model,
                collection_name=domain,
                client=client
            )

            docs_saver = DocsSaver(            
                documents=docs,
                vector_store=vector_store,
                batch_size=100
            )
            docs_saver.saveChunksToVectoreStoreWithoutContext()

    finally:
        client.close()



if __name__ == "__main__":
    main()
