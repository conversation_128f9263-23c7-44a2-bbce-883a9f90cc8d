import time
from tqdm import tqdm

from src.utils.doc_with_source import DocWithSource
from src.utils.docs_convert_langchainDocument import ConvertDocsToLangchainDocuments, ConvertDocsToLangchainDocumentsWithoutContext

class DocsSaver:
    def __init__(self, documents: list[DocWithSource], vector_store, batch_size=100):
        self.documents = documents
        self.vector_store = vector_store
        self.batch_size = batch_size

    def saveChunksToVectoreStore(self, ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocuments):
        langchainDocuments = ConvertDocsToLangchainDocumentsFunction(self.documents)

        num_batches = (len(langchainDocuments) + self.batch_size - 1) // self.batch_size
        for i in tqdm(range(0, len(langchainDocuments), self.batch_size), desc="Uploading batches", total=num_batches):
            batch = langchainDocuments[i:i+self.batch_size]
            self.vector_store.add_documents(batch)
            time.sleep(3)

    def saveChunksToVectoreStoreWithContext(self):
        self.saveChunksToVectoreStore(ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocuments)

    def saveChunksToVectoreStoreWithoutContext(self):
        self.saveChunksToVectoreStore(ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocumentsWithoutContext)
