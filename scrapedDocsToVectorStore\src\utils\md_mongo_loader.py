import pymongo
from pymongo import MongoClient

from src.utils.doc_with_source import DocWithSource


class MDMongoLoader:
    def __init__(self, mongo_url: str, database_name: str):
        self.mongo_url = mongo_url
        self.database_name = database_name

    def get_latest_collection_name(self, prefix: str) -> str:
        """    
        Returns:
            Latest collection name or empty string if no collections found
        """
        try:
            # Connect to MongoDB
            client = MongoClient(self.mongo_url)
            db = client[self.database_name]
            

            # Get all collection names
            collections = db.list_collection_names()

            # Filter and find the latest collection
            valid_collections = [
                name for name in collections 
                if name.startswith(prefix) and len(name) == (16 + len(prefix))
            ]
            
            if not valid_collections:
                return ""
                
            # Sort collections by name (since format YYYYMMDD_HHMMSS will sort chronologically)
            latest_collection = sorted(valid_collections)[-1]
            
            return latest_collection
            
        except Exception as e:
            print(f"Error connecting to MongoDB: {e}")
            return ""
        finally:
            client.close()

    def loadAllMarkDown_fromMongoDB(self, collectionName: str) -> list[DocWithSource]:
        try:
            # MongoDB kapcsolódás
            client = pymongo.MongoClient(self.mongo_url)
            db = client[self.database_name]
            collection = db[collectionName]

            # Retrieve all documents from the collection
            documents = collection.find()

            datas=[]

            # Process each document
            for doc in documents:
                document = DocWithSource(
                    source=doc.get("url", ""),
                    domain=doc.get("domain", ""),
                    content = doc.get("md", ""),  # Assuming the Markdown content is stored in a field called 'content'
                    chunkList=[],
                    contextList=[]
                )
                
                datas.append(document)

            return datas
        finally:
            client.close()