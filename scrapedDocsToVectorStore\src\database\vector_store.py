from http import client
from langchain_community.vectorstores.timescalevector import TimescaleVector

from src.utils.index_for_vector import ensure_vector_index_hnsw

from langchain_weaviate.vectorstores import WeaviateVectorStore

class WeaviateStore:
    def __init__(self, embedding_model, collection_name, client):
        self.embedding_model = embedding_model
        self.collection_name = collection_name
        self.client = client
        try:
            if self.client.collections.exists(self.collection_name):
                self.client.collections.delete(self.collection_name)
                print(f"[Warning] Collection '{self.collection_name}' already exists and has been deleted.")

            self.vector_store = WeaviateVectorStore(
                embedding=self.embedding_model,
                client=self.client,
                text_key="content",
                index_name=self.collection_name,
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
        else:
            print("[Warning] Vector store not available; documents not added.")

    def final_move(self):
        if self.client:
            self.client.close()
        else:
            print("[Warning] No client to close.")

class TimescaleDB:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name):
        self.embedding_model = embedding_model
        self.service_url = service_url
        self.num_dimensions = num_dimensions
        self.table_name = table_name
        try:
            self.vector_store = TimescaleVector(
                embedding=self.embedding_model,
                service_url=self.service_url,
                collection_name=self.table_name,
                num_dimensions=self.num_dimensions
            )
            print(f"[Info] Initialized TimescaleDB vector store with collection '{self.table_name}'.")
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents, create_index=True):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
            if create_index:
                ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; documents not added.")
    
    def final_move(self):
        if self.vector_store:
            ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; index not created.")

class VectorStoreManager:
    def __init__(self, store_type, embedding_model, collection_name, **kwargs):
        self.embedding_model = embedding_model
        self.collection_name = collection_name
        
        if store_type == "weaviate":
            self.vector_store = WeaviateStore(
                embedding_model=embedding_model,
                collection_name=collection_name,
                client=kwargs.get("client")
            )

    def add_documents(self, langchain_documents):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents=langchain_documents)
        else:
            print("[Warning] Vector store not available; documents not added.")

    def final_move(self):
        if self.vector_store:
            self.vector_store.final_move()
        else:
            print("[Warning] Vector store not available; final move not executed.")