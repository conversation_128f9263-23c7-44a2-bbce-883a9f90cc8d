apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-search
  labels:
    app: sales-agent
    component: search
    tier: backend
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: search
      tier: backend
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: search
        tier: backend
        environment: production
    spec:
      containers:
        - image: mcp-search:latest
          name: mcp-search
          imagePullPolicy: Never
          envFrom:
            - secretRef:
                name: weaviate-secrets
          env:
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: api-secrets
                  key: GOOGLE_API_KEY
          ports:
            - containerPort: 8000
              protocol: TCP
