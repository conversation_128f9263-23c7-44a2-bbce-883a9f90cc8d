# Set this to limit the sites you want to scrape
# CLOSESPIDER_ITEMCOUNT = 15

from dotenv import load_dotenv
load_dotenv()

import os

# Mongodb Connection details
MONGODB_URI = os.getenv("MONGODB_URI", None)
MONGODB_DATABASE = os.getenv("MONGODB_DATABASE", None)
# PostgreSQL Connection details
POSTGRES_HOST=os.getenv("POSTGRES_HOST", None)
POSTGRES_PORT=os.getenv("POSTGRES_PORT", None)
POSTGRES_DB=os.getenv("POSTGRES_DB", None)
POSTGRES_USER=os.getenv("POSTGRES_USER", None)
POSTGRES_PASSWORD=os.getenv("POSTGRES_PASSWORD", None)

# Scrapy settings for graphibot project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html
BOT_NAME = "graphibot"

SPIDER_MODULES = ["graphibot.spiders"]
NEWSPIDER_MODULE = "graphibot.spiders"

# Crawl responsibly by identifying yourself (and your website) on the user-agent
USER_AGENT = "GraphiBot (+https://graphisoft.com/)"

# Set logging level
LOG_LEVEL = "INFO"

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# Configure maximum concurrent requests performed by Scrapy (default: 16)
#CONCURRENT_REQUESTS = 32

# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
#DOWNLOAD_DELAY = 1
# The download delay setting will honor only one of:
#CONCURRENT_REQUESTS_PER_DOMAIN = 16
#CONCURRENT_REQUESTS_PER_IP = 16

# Enable and configure the AutoThrottle extension (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
AUTOTHROTTLE_ENABLED = True
# The initial download delay
AUTOTHROTTLE_START_DELAY = 0.5
# The maximum download delay to be set in case of high latencies
AUTOTHROTTLE_MAX_DELAY = 60
# The average number of requests Scrapy should be sending in parallel to
# each remote server
AUTOTHROTTLE_TARGET_CONCURRENCY = 16.0
# Enable showing throttling stats for every response received:
AUTOTHROTTLE_DEBUG = False

# Disable cookies (enabled by default)
#COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
TELNETCONSOLE_ENABLED = False

# Override the default request headers:
#DEFAULT_REQUEST_HEADERS = {
#    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
#    "Accept-Language": "en",
#}

# Enable or disable spider middlewares
# See https://docs.scrapy.org/en/latest/topics/spider-middleware.html
#SPIDER_MIDDLEWARES = {
#    "graphibot.middlewares.GraphibotSpiderMiddleware": 543,
#}

# Enable or disable downloader middlewares
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
# DOWNLOADER_MIDDLEWARES = {
#    "graphibot.middlewares.IgnoreSubdomainDownloadMiddleware": 543,
#    "graphibot.middlewares.IgnoreCountryCodeDownloadMiddleware": 544,
#    "graphibot.middlewares.IgnoreNonHtmlDownloadMiddleware": 545,
# }

# Enable or disable extensions
# See https://docs.scrapy.org/en/latest/topics/extensions.html
#EXTENSIONS = {
#    "scrapy.extensions.telnet.TelnetConsole": None,
#}

# Configure item pipelines
# See https://docs.scrapy.org/en/latest/topics/item-pipeline.html
ITEM_PIPELINES = {
    # "graphibot.pipelines.MongoDBPipeline": 100,
    # "graphibot.pipelines.PostgreSQLPipeline": 100,
    # "graphibot.pipelines.JsonWriterPipeline": 900,
}

# Enable and configure HTTP caching (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
#HTTPCACHE_ENABLED = True
#HTTPCACHE_EXPIRATION_SECS = 0
#HTTPCACHE_DIR = "httpcache"
#HTTPCACHE_IGNORE_HTTP_CODES = []
#HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"

# Set settings whose default value is deprecated to a future-proof value
TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
FEED_EXPORT_ENCODING = "utf-8"
