from langchain_core.runnables import RunnableConfig
from src.tools_agent.configuration import AgentConfig
from pydantic import ValidationError

from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent

async def make_graph(config: RunnableConfig):
    # Extract config and validate it
    configurable = config.get("configurable", {})
    try:
        agent_config = AgentConfig(**configurable)
    except ValidationError as e:
        print(f"Error validating agent config: {e}")
        return None
    
    # Initialize the chat model
    model_name = agent_config.model
    chat_model = init_chat_model(model_name)
    system_prompt = agent_config.system_prompt
    
    # Convert from list to dict of configs keyed by server name 
    mcp_list = agent_config.mcp_configs
    mcp_dict = {
        mcp.name: {
            "url": mcp.url,
            "transport": mcp.transport,
        } for mcp in mcp_list
    }
    mcp_client = MultiServerMCPClient(mcp_dict)
    tools = await mcp_client.get_tools()

    # Create the agent with the provided configuration
    agent = create_react_agent(
        model = chat_model,
        prompt = system_prompt,
        tools = tools,
        config_schema = AgentConfig,
    )

    return agent
