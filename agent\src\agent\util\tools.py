from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState

from agent.util.states import State, OfferParams
from typing import Annotated

@tool
def update_offer(
    params: OfferParams,
    state: Annotated[State, InjectedState],
):
    """This is a tool to update parameters of the offer we are providing to the user. 
    Update parameters the user tells you, like their country, the subscripton, the term (monthly, yearly, 3 years) and number of users.
    All other parameters must be exactly as they were in the previous offer, not null.
    Always call this tool with the full set of parameters: the updated ones from the user, and the unchanged ones from the previous offer.
    The price will must be recalculated using the calculate_price tool, and the price offer must be updated using the update_price tool.
    """
    offer = state.offer

    # Update only the parameters that are provided in `params`
    if params.country is not None:
        offer.country = params.country
    if params.subscription is not None:
        offer.subscription = params.subscription
    if params.term is not None:
        offer.term = params.term
    if params.seats is not None:
        offer.seats = params.seats
    
    offer.price = None
    state.offer = offer
    # Maybe wrap this in ToolMessage?
    return "Successfully updated parameters!"

@tool 
def update_price(
    price: float,
    currency: str,
    state: Annotated[State, InjectedState],
):
    """This is a tool to update the price and currency of the offer we are providing to the user."""
    offer = state.offer
    offer.price = price
    offer.currency = currency

    return "Successfully updated price and currency!"

TOOLS = [
    update_offer,
    update_price,
]
