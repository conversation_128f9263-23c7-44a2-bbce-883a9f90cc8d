2025-07-25T14:22:31.261555Z [error    ] Error reading SSE stream:      [mcp.client.streamable_http] api_revision=5242aa5 api_variant=licensed langgraph_api_version=0.2.103 method=POST path=/threads/{thread_id}/history request_id=a88b1db5-bea0-4720-9207-93ae5f8e84d8 thread_name=MainThread

Traceback (most recent call last):

  File "/usr/local/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions

    yield

  File "/usr/local/lib/python3.11/site-packages/httpx/_transports/default.py", line 271, in __aiter__

    async for part in self._httpcore_stream:

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/connection_pool.py", line 407, in __aiter__

    raise exc from None

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/connection_pool.py", line 403, in __aiter__

    async for part in self._stream:

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/http11.py", line 342, in __aiter__

    raise exc

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/http11.py", line 334, in __aiter__

    async for chunk in self._connection._receive_response_body(**kwargs):

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/http11.py", line 203, in _receive_response_body

    event = await self._receive_event(timeout=timeout)

            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

  File "/usr/local/lib/python3.11/site-packages/httpcore/_async/http11.py", line 213, in _receive_event

    with map_exceptions({h11.RemoteProtocolError: RemoteProtocolError}):

  File "/usr/local/lib/python3.11/contextlib.py", line 158, in __exit__

    self.gen.throw(typ, value, traceback)

  File "/usr/local/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions

    raise to_exc(exc) from exc

httpcore.RemoteProtocolError: peer closed connection without sending complete message body (incomplete chunked read)

