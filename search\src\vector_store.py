from langchain_weaviate.vectorstores import WeaviateVectorStore

class WeaviateStore:
    def __init__(self, embedding_model, collection_name, client):
        self.embedding_model = embedding_model
        self.collection_name = collection_name
        self.client = client
        try:

            self.vector_store = WeaviateVectorStore(
                embedding=self.embedding_model,
                client=self.client,
                text_key="content",
                index_name=self.collection_name,
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None