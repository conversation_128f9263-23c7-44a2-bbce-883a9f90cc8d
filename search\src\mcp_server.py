import os
from fastmcp import Fast<PERSON>P, Context
from contextlib import asynccontextmanager
from collections.abc import Async<PERSON>terator
from dataclasses import dataclass
from langchain_core.documents import Document

from typing import List

from vector_search import SearchService

from dotenv import load_dotenv

load_dotenv()

# Create an MCP server
mcp = FastMCP("RAG search service")

@dataclass
class AppContext:
    vector_search: SearchService

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    vector_search = SearchService()
    try:
        yield AppContext(vector_search=vector_search)
    finally:
        vector_search.client_close()

# Pass lifespan to server
mcp = FastMCP("RAG search service", lifespan=app_lifespan)

@mcp.tool()
def list_companies(
    ctx: Context,
):
    """
    This tool is used for listing the companies that are available in the RAG database.
    
    Returns:
        A list of company names that are available in the RAG database.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    results=vector_search.get_available_companies()
    return results


@mcp.tool()
def search_for_context(
    ctx: Context,
    company: str,
    query: str,
) -> List[Document]:
    """
    This tool is used for searching and gathering information from the RAG database mainly about Graphisoft and its products.
    
    Args:
        company: The company name which the query is about.
        query: Based on the given query, the tool will search for relevant information in the database with vectorsearch retriever.    
    Returns:
        A list of chunks which come from webpages and transformed into markdown format and the sources of the webpages where chunks come from.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    company=company.lower()

    if company not in vector_search.get_available_companies():
        raise ValueError(f"Company '{company}' is not available. Available companies are: {', '.join(vector_search.get_available_companies())}")

    # Perform a similarity search using all the rephrased queries
    results = vector_search.search_query(domain=company, query=query, k=20)

    return results

@mcp.tool()
def get_full_page_content(
    ctx: Context,
    company: str,
    source: str,
) -> List[Document]:
    """
    If from the "search_for_context_by_query" results you find that a webpage all chunks would be useful, USE THIS TOOL to get all chunks by the source of the chunk website!
    
    Args:
        company: The company name which the query is about.
        source: The source (webpage url) of the chunks.
    Returns:
        A list of chunks which come from the webpage that source you searched for (and transformed into markdown format and the sources of the webpages where chunks come from).
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    company=company.lower()

    if company not in vector_search.get_available_companies():
        raise ValueError(f"Company '{company}' is not available. Available companies are: {', '.join(vector_search.get_available_companies())}")

    # Perform a similarity search using all the rephrased queries
    results = vector_search.search_chunks_by_source(domain=company, source=source)

    return results


if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)