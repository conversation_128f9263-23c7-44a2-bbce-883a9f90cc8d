import scrapy
import json
import re
import os

TERM_MAPPING = {
    "Monthly": {"id": "monthly", "commitment_value": 1, "commitment_unit": "months", "billing_cycle": "monthly"},
    "1-year": {"id": "1-year", "commitment_value": 12, "commitment_unit": "months", "billing_cycle": "annual"},
    "3-year": {"id": "3-year", "commitment_value": 36, "commitment_unit": "months", "billing_cycle": "annual"},
}

class GraphisoftPricingSpider(scrapy.Spider):
    name = "graphisoft_pricing_spider"
    buy_now_url = "https://www.graphisoft.com/buy-now"
    ajax_url = "https://www.graphisoft.com/cms/wp-admin/admin-ajax.php" # As extracted from GS.ajaxurl from buy-now page on 2025-06-16
    custom_settings = {
        "ITEM_PIPELINES": {
            "graphibot.pipelines.JsonMergePipeline": 100,
        },
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        base_dir = os.path.dirname(__file__)
        path = os.path.join(base_dir, "graphisoft_pricing_records_data.json")

        with open(path, "r", encoding="utf-8") as f:
            self.records = json.load(f)
        self.logger.info(f"Loading countries data from: {path}")

    async def start(self):
        for idx, record in enumerate(self.records):
            code = record["cca2"]
            region = record.get("region", "")

            yield scrapy.FormRequest(
                url = self.ajax_url,
                method ="POST",
                formdata = {
                    "action": "select-country",
                    "code": code,
                    "region": region,
                },
                callback = self.handle_redirect,
                cb_kwargs = {"record": record},
                dont_filter = True,
                meta = {"cookiejar": idx},
            )

    def handle_redirect(self, response, record):
        yield scrapy.Request(
            url = self.buy_now_url,
            callback = self.parse_buy_now,
            cb_kwargs= {"record": record},
            dont_filter = True,
            meta = {"cookiejar": response.meta["cookiejar"]},
        )

    def parse_buy_now(self, response, record):
        code = record["cca2"]
        region = record.get("region", None)
        common_name = record["name"]["common"]
        
        location_key = f"{code}-{region}" if region else code
        country_name_display = f"{common_name} ({region})" if region else common_name
        self.logger.info(f"Scraping buy-now page | location: {location_key}")

        # Extract terms (columns of licencing table)
        term_headers = []
        for header in response.css("table.licensing-table thead tr.licensing-table-terms th.licensing-header"):
            term = header.css("p:first-child::text").get().strip()
            term_headers.append(term)

        # Extract subscriptions (rows of licencing table)
        subscriptions = {}
        for row in response.css("table.licensing-table tbody tr"):
            subscription_name = row.css("td a.licensing-table-links::text").get()
            if not subscription_name:
                continue
            subscription_name = subscription_name.strip()

            terms = []
            for idx, term_name in enumerate(term_headers, start = 2): # Start from 2 to skip the first column (subscription name)
                price_span = row.css(f"td:nth-child({idx}) span:first-child::text").get()
                price_per_month_str = price_span.strip() if price_span else None
                price_per_month, currency = self.extract_price_and_currency(price_per_month_str)

                pay_now_span = row.css(f"td:nth-child({idx}) .licensing-pay-text:contains('Pay now')::text").get()
                pay_now_str = pay_now_span.replace("Pay now: ", "").strip() if pay_now_span else None
                pay_now, _ = self.extract_price_and_currency(pay_now_str)

                total_price_span = row.css(f"td:nth-child({idx}) .licensing-pay-text:contains('Total price')::text").get()
                total_price_str = total_price_span.replace("Total price: ", "").strip() if total_price_span else None
                total_price, _ = self.extract_price_and_currency(total_price_str)

                if not price_per_month:
                    continue

                term_info = TERM_MAPPING.get(term_name)
                if not term_info:
                    self.logger.warning(f"Unknown term: {term_name} for subscription: {subscription_name}")
                    continue

                term_object = {
                    "term_id": term_info["id"],
                    "commitment": {
                        "value": term_info["commitment_value"],
                        "unit": term_info["commitment_unit"],
                    },
                    "billing": {
                        "cycle": term_info["billing_cycle"],
                        "price_per_cycle": price_per_month if term_info["billing_cycle"] == "monthly" else pay_now,
                    },
                    "price_per_month": price_per_month,
                    "total_price": price_per_month if term_info["billing_cycle"] == "monthly" else total_price,
                    "currency": currency,
                }

                terms.append(term_object)
            
            subscriptions[subscription_name] = {"terms": terms}

        location_data = {
            "country_name": country_name_display,
            "subscriptions": subscriptions,
        }

        yield {location_key: location_data}

    def extract_price_and_currency(self, price_str):
        """
        Extract numeric price value and currency from a price string.
        
        Args:
            price_str (str): The price string to parse (e.g., "HUF 87,000")
            
        Returns:
            tuple: (price_value, currency) where price_value is a float and currency is a string
        """
        price_value = None
        currency = None
        
        if not price_str:
            return price_value, currency
            
        # Extract the price value
        numeric_pattern = r'[\d,.]+'
        numeric_matches = re.findall(numeric_pattern, price_str)
        if numeric_matches:
            # Convert to float, removing commas
            price_value = float(numeric_matches[0].replace(',', ''))
        
        # Extract currency using common patterns
        # Order matters: more specific patterns (like CA$) should come before generic ones (like $)
        currency_patterns = {
            r'HUF': 'HUF',     # Hungarian Forint
            r'MX\$': 'MXN',     # Mexican Peso
            r'R\$': 'BRL',     # Brazilian Real
            r'CA\$': 'CAD',     # Canadian Dollar
            r'HK\$': 'HKD',     # Hong Kong Dollar
            r'NZ\$': 'NZD',     # New Zealand Dollar
            r'A\$': 'AUD',     # Australian Dollar
            r'€': 'EUR',       # Euro
            r'£': 'GBP',       # British Pound
            r'¥': 'JPY',       # Japanese Yen
            r'\$': 'USD'       # US Dollar
        }

        currency_found = False
        for pattern, curr in currency_patterns.items():
            if re.search(pattern, price_str):
                currency = curr
                currency_found = True
                break
        
        # If no common currency pattern matched, extract non-numeric part
        if not currency_found:
            # Remove digits, commas, dots, spaces, plus signs, and "tax", "seat" or "/"
            non_numeric = re.sub(r'[\d,.\s+]|tax|seat|/', '', price_str).strip()
            if non_numeric:
                currency = non_numeric[:3].upper()
                
        return price_value, currency
