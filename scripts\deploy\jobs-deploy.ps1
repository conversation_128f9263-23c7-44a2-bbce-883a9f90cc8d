$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

function Wait-K8sJobCompletion {
    param(
        [Parameter(Mandatory=$true)]
        [string]$JobName,
        [int]$TimeoutSeconds = 1800,
        [int]$SleepSeconds = 10
    )

    Write-Host "Waiting for Kubernetes Job '$JobName' to complete (timeout: $TimeoutSeconds seconds)..."

    $startTime = Get-Date
    do {
        $elapsedTime = (New-TimeSpan -Start $startTime -End (Get-Date)).TotalSeconds
        if ($elapsedTime -ge $TimeoutSeconds) {
            Write-Warning "Timeout reached for job '$JobName'."
            kubectl logs job/$JobName | Out-String | Write-Host
            return $false
        }

        try {
            $jobStatusJson = kubectl get job $JobName -o json
            $jobObject = $jobStatusJson | ConvertFrom-Json

            if ($jobObject -and $jobObject.status) {
                if ($jobObject.status.succeeded -ge 1) {
                    Write-Host "Job '$JobName' succeeded."
                    return $true
                }

                $failedCondition = $jobObject.status.conditions | Where-Object { $_.type -eq "Failed" -and $_.status -eq "True" }
                if ($failedCondition) {
                    Write-Error "Job '$JobName' failed."
                    kubectl logs job/$JobName | Out-String | Write-Host
                    return $false
                }

                Write-Host "Job '$JobName' is still running. Elapsed: $([math]::Round($elapsedTime)) seconds."
            } else {
                Write-Host "Job '$JobName' status not yet available or in unexpected state. Retrying..."
            }
        }
        catch {
            Write-Warning "Could not get status for job '$JobName'. Error: $($_.Exception.Message). Retrying..."
        }

        Start-Sleep -Seconds $SleepSeconds
    } while ($true)
}

Write-Host "`n--- Applying Kubernetes Job Configurations ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\jobs")

    Write-Host "Applying mcp-pricing-init-sql-configmap.yaml..."
    kubectl apply -f mcp-pricing-init-sql-configmap.yaml
    Write-Host "mcp-pricing-init-sql-configmap.yaml applied."

    $pricingScrapyJobName = "mcp-pricing-scrapy-job"
    kubectl apply -f "$pricingScrapyJobName.yaml"
    Write-Host "$pricingScrapyJobName.yaml applied."
    if (-not (Wait-K8sJobCompletion -JobName $pricingScrapyJobName -TimeoutSeconds 100)) {
        Write-Error "Failed or timed out waiting for $pricingScrapyJobName. Exiting."
        exit 1
    }

    $scrapedWebsitesScrapyJobName = "scraped-websites-scrapy-job"
    kubectl apply -f "$scrapedWebsitesScrapyJobName.yaml"
    Write-Host "$scrapedWebsitesScrapyJobName.yaml applied."
    if (-not (Wait-K8sJobCompletion -JobName $scrapedWebsitesScrapyJobName -TimeoutSeconds 600)) {
        Write-Error "Failed or timed out waiting for $scrapedWebsitesScrapyJobName. Exiting."
        exit 1
    }

    $vectorizerJobName = "mcp-search-vectorizer-pipeline-job"
    kubectl apply -f "$vectorizerJobName.yaml"
    Write-Host "$vectorizerJobName.yaml applied."
    if (-not (Wait-K8sJobCompletion -JobName $vectorizerJobName -TimeoutSeconds 1800)) {
        Write-Error "Failed or timed out waiting for $vectorizerJobName. Exiting."
        exit 1
    }

    Pop-Location
    Write-Host "All Kubernetes job configurations applied and completed successfully."
}
catch {
    Write-Error "Error applying job configurations or waiting for jobs: $($_.Exception.Message)"
    exit 1
}
